import z from 'zod';
import { ChatMessageSchema, MessageRoleSchema, MessageSchema } from './message';

export const ConversationTypeSchema = z.enum(['default', 'remote', 'miniapp']);
export type ConversationType = z.infer<typeof ConversationTypeSchema>;

export const ConversationSchema = z.object({
  id: z.number(),
  title: z.string().nullable().optional(),
  type: ConversationTypeSchema.nullable().optional(),
  last_selected_at: z.number().nullable().optional(),
  messages: z.array(MessageSchema).nullable().optional(),
});
export type Conversation = z.infer<typeof ConversationSchema>;

// conversation handlers
export const CreateConversationRequestSchema = z.object({
  id: z.number(),
  type: ConversationTypeSchema.nullable().optional(),
  last_selected_at: z.number().nullable().optional(),
});
export type CreateConversationRequest = z.infer<typeof CreateConversationRequestSchema>;

export const CreateConversationResponseSchema = z.object({
  id: z.number(),
});
export type CreateConversationResponse = z.infer<typeof CreateConversationResponseSchema>;

export const DeleteConversationRequestSchema = z.object({
  id: z.number(),
});
export type DeleteConversationRequest = z.infer<typeof DeleteConversationRequestSchema>;

export const DeleteConversationResponseSchema = z.object({
  deleted: z.boolean(),
});
export type DeleteConversationResponse = z.infer<typeof DeleteConversationResponseSchema>;

export const ListConversationsRequestSchema = z.object({
  startFrom: z.number().default(0),
});
export type ListConversationsRequest = z.infer<typeof ListConversationsRequestSchema>;

export const ListConversationsResponseSchema = z.object({
  conversations: z.array(ConversationSchema),
});
export type ListConversationsResponse = z.infer<typeof ListConversationsResponseSchema>;

export const UpdateConversationRequestSchema = z.object({
  id: z.number(),
  last_selected_at: z.number().optional(),
  title: z.string().optional(),
  type: ConversationTypeSchema.optional(),
  status: z.string().optional(),
});
export type UpdateConversationRequest = z.infer<typeof UpdateConversationRequestSchema>;

export const UpdateConversationResponseSchema = z.object({
  success: z.boolean(),
});
export type UpdateConversationResponse = z.infer<typeof UpdateConversationResponseSchema>;

// Chat completion parameters
export const ChatCompletionCreateParamSchema = z.object({
  messages: z.array(ChatMessageSchema),
  model: z.string(),
  frequency_penalty: z.number().nullable().optional(),
  logit_bias: z.record(z.number()).nullable().optional(),
  max_tokens: z.number().nullable().optional(),
  n: z.number().nullable().optional(),
  presence_penalty: z.number().nullable().optional(),
  response_format: z.record(z.string()).nullable().optional(),
  seed: z.number().nullable().optional(),
  stop: z
    .union([z.string(), z.array(z.string())])
    .nullable()
    .optional(),
  stream: z.boolean().default(false),
  temperature: z.number().nullable().optional(),
  top_p: z.number().nullable().optional(),
  tools: z.array(z.record(z.unknown())).nullable().optional(),
  tool_choice: z
    .union([z.string(), z.record(z.unknown())])
    .nullable()
    .optional(),
  user: z.string().nullable().optional(),
});

export type ChatCompletionCreateParam = z.infer<typeof ChatCompletionCreateParamSchema>;

export const ChatCompletionResponseSchema = z.object({
  id: z.string(),
  object: z.string(),
  created: z.number(),
  model: z.string(),
  choices: z.array(
    z.object({
      index: z.number(),
      message: z.object({
        role: MessageRoleSchema,
        content: z.string(),
      }),
      finish_reason: z.string().nullable(),
    })
  ),
  usage: z.object({
    prompt_tokens: z.number(),
    completion_tokens: z.number(),
    total_tokens: z.number(),
  }),
});
export type ChatCompletionResponse = z.infer<typeof ChatCompletionResponseSchema>;
