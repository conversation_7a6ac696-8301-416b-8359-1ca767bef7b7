import z from 'zod';
import { ConversationSchema } from './chat';

export const DevelopingSchema = z.object({
  code: z.string(),
  version: z.number(),
  updated_at: z.number(),
});
export type Developing = z.infer<typeof DevelopingSchema>;

export const InstallationSchema = z.object({
  code: z.string(),
  changelogs: z.string(),
  deployed_at: z.number(),
});
export type Installation = z.infer<typeof InstallationSchema>;

export const MiniAppStatusSchema = z.enum(['active', 'archived', 'deleted', 'testing']);
export type MiniAppStatus = z.infer<typeof MiniAppStatusSchema>;

export const MiniAppTypeSchema = z.enum(['workflow', 'userScript']);
export type MiniAppType = z.infer<typeof MiniAppTypeSchema>;

export const MiniAppSchema = z.object({
  id: z.number(),
  name: z.string(),
  conversation_id: z.number(),
  developing: DevelopingSchema.nullable().optional(),
  installed_at: z.string().nullable().optional(),
  installation: InstallationSchema.nullable().optional(),
  history: z.array(InstallationSchema),
  status: MiniAppStatusSchema.nullable().optional(),
  type: MiniAppTypeSchema.nullable().optional(),
});
export type MiniApp = z.infer<typeof MiniAppSchema>;

export const ListMiniappResponseType = MiniAppSchema.extend({
  conversation: ConversationSchema,
});
export type ListMiniappResponseType = z.infer<typeof ListMiniappResponseType>;

export const SaveMiniAppRequestSchema = z.object({
  miniapp: MiniAppSchema,
});
export type SaveMiniAppRequest = z.infer<typeof SaveMiniAppRequestSchema>;

export const SaveMiniAppResponseSchema = z.object({
  success: z.boolean(),
});
export type SaveMiniAppResponse = z.infer<typeof SaveMiniAppResponseSchema>;

// Update MiniApp
export const UpdateMiniAppRequestSchema = z.object({
  id: z.number(),
  name: z.string().optional(),
  developing: DevelopingSchema.nullable().optional(),
  installation: InstallationSchema.nullable().optional(),
  status: MiniAppStatusSchema.nullable().optional(),
});
export type UpdateMiniAppRequest = z.infer<typeof UpdateMiniAppRequestSchema>;

export const UpdateMiniAppResponseSchema = z.object({
  success: z.boolean(),
});
export type UpdateMiniAppResponse = z.infer<typeof UpdateMiniAppResponseSchema>;
