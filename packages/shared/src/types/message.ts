import z from 'zod';

// Common types
export const MessageRoleSchema = z.enum(['system', 'user', 'assistant', 'tool']);
export type MessageRole = z.infer<typeof MessageRoleSchema>;

export const MessageStatusSchema = z.enum(['pending', 'completed', 'error', 'warning']);
export type MessageStatus = z.infer<typeof MessageStatusSchema>;

export const ToolCallResultSchema = z.object({
  success: z.boolean(),
  data: z.unknown().nullable().optional(),
  error: z.string().nullable().optional(),
});
export type ToolCallResult = z.infer<typeof ToolCallResultSchema>;

export const ToolCallSchema = z.object({
  id: z.string(),
  type: z.string(),
  function: z.object({
    name: z.string(),
    arguments: z.string(),
  }),
  result: ToolCallResultSchema.nullable().optional(),
});
export type ToolCall = z.infer<typeof ToolCallSchema>;

export const TokenUsageSchema = z.object({
  prompt_tokens: z.number(),
  completion_tokens: z.number(),
  total_tokens: z.number(),
});
export type TokenUsage = z.infer<typeof TokenUsageSchema>;

export const ChatMessageSchema = z.object({
  role: MessageRoleSchema,
  content: z.string().nullable().optional(),
  reasoning: z.string().nullable().optional(),
  name: z.string().nullable().optional(),
  tool_calls: z.array(ToolCallSchema).nullable().optional(),
  tool_call_id: z.string().nullable().optional(),
  token_usage: TokenUsageSchema.nullable().optional(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;

export const MessageContentPartSchema = z.union([
  z.object({
    type: z.literal('text'),
    text: z.string(),
  }),
  z.object({
    type: z.literal('image_url'),
    image_url: z.object({ url: z.string() }),
  }),
]);
export type MessageContentPart = z.infer<typeof MessageContentPartSchema>;

export const MessageContentSchema = z.union([z.string(), z.array(MessageContentPartSchema)]);
export type MessageContent = z.infer<typeof MessageContentSchema>;

export const MessageSchema = ChatMessageSchema.extend({
  id: z.number(),
  conversation_id: z.number(),
  run_id: z.string().nullable().optional(),
  agent_id: z.string().nullable().optional(),
  actor: z.enum(['system', 'user']).nullable().optional(),
  task_id: z.string().nullable().optional(),
  status: MessageStatusSchema.nullable().optional(),
  error: z.string().nullable().optional(),
  metadata: z.record(z.unknown()).nullable().optional(),
  // deprecated
  display_text: z.string().nullable().optional(),
  tool_call_arguments: z.string().nullable().optional(),
  tool_call_result: z.any().nullable().optional(),
});
export type Message = z.infer<typeof MessageSchema>;

// message handlers
export const SaveMessageRequestSchemaV2 = z.object({
  message: MessageSchema,
});
export type SaveMessageRequestV2 = z.infer<typeof SaveMessageRequestSchemaV2>;

export const SaveMessageResponseSchemaV2 = z.object({
  success: z.boolean(),
});
export type SaveMessageResponseV2 = z.infer<typeof SaveMessageResponseSchemaV2>;
