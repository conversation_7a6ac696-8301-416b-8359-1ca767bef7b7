import z from 'zod';

export const TemplateRegisterRequestSchema = z.object({
  domain: z.string(),
  path: z.string(),
  tag_vector: z.array(z.number()).length(128),
  simhash: z.number(),
});
export type TemplateRegisterRequest = z.infer<typeof TemplateRegisterRequestSchema>;

export const TemplateSchema = z.object({
  id: z.number().int().positive(),
  domain: z.string(),
  path_pattern: z.string(),
});
export type Template = z.infer<typeof TemplateSchema>;

export const GetTemplatesRequestSchema = z.object({
  domain: z.string().nullable().optional(),
});
export type GetTemplatesRequest = z.infer<typeof GetTemplatesRequestSchema>;

export const TemplateRegisterResponseSchema = TemplateSchema.extend({
  matched: z.boolean(),
});
export type TemplateRegisterResponse = z.infer<typeof TemplateRegisterResponseSchema>;

export const GetTemplatesResponseSchema = z.object({
  templates: z.array(TemplateSchema),
});
export type GetTemplatesResponse = z.infer<typeof GetTemplatesResponseSchema>;
