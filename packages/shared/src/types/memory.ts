import { z } from 'zod';
import { MessageSchema } from './message';

export interface AddMemoryOptions {
  metadata?: Record<string, any>;
  filters?: SearchFilters;
}

export const MemoryTypeSchema = z.enum(['procedural', 'semantic', 'site']);
export type MemoryType = z.infer<typeof MemoryTypeSchema>;

export const SearchFiltersSchema = z.object({
  userId: z.string().optional(),
  conversationId: z.string().optional(),
  hostname: z.string().optional(),
  memoryType: MemoryTypeSchema.optional(),
  taskId: z.string().optional(),
  agentId: z.string().optional(),
  runId: z.string().optional(),
  workflowId: z.string().optional(),
});

export type SearchFilters = z.infer<typeof SearchFiltersSchema>;

export const SearchMemoryOptionsSchema = z.object({
  limit: z.number(),
  filters: SearchFiltersSchema,
});

export const AddMemoryOptionsSchema = z.object({
  metadata: z.record(z.any()).optional(),
  filters: SearchFiltersSchema,
});

export const EntitySchema = z.object({
  userId: z.string().optional(),
  agentId: z.string().optional(),
  runId: z.string().optional(),
  taskId: z.string().optional(),
  workflowId: z.string().optional(),
  conversationId: z.string().optional(),
  hostname: z.string().optional(),
  memoryType: MemoryTypeSchema.optional(),
});
export type Entity = z.infer<typeof EntitySchema>;

export const MemoryMetadataSchema = z
  .object({
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
  })
  .passthrough();
export type MemoryMetadata = Entity & z.infer<typeof MemoryMetadataSchema>;

export const MemoryItemSchema = z.object({
  id: z.string(),
  memory: z.string(),
  hash: z.string().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  score: z.number().optional(),
  metadata: MemoryMetadataSchema.optional(),
});
export type MemoryItem = z.infer<typeof MemoryItemSchema>;

export interface MemoryRelation {
  source: string;
  relationship: string;
  destination: string;
}

export interface SearchResult {
  results: MemoryItem[];
  relations?: MemoryRelation[];
}

export interface SearchMemoryOptions {
  limit?: number;
  filters?: SearchFilters;
}

export const GetAllMemoryOptionsSchema = z.object({
  limit: z.number().optional(),
});
export type GetAllMemoryOptions = z.infer<typeof GetAllMemoryOptionsSchema>;

export const MemoryConfigSchema = z.object({
  filters: EntitySchema.optional(),
  metadata: z.union([MemoryMetadataSchema, z.record(z.unknown())]).optional(),
});
export type MemoryConfig = z.infer<typeof MemoryConfigSchema>;

export const SearchMemoryRequestSchema = z.object({
  text: z.string(),
  limit: z.number().default(3),
  conversationId: z.string(),
});
export type SearchMemoryRequest = z.infer<typeof SearchMemoryRequestSchema>;

export const SearchMemoryResponseSchema = z.object({
  results: z.array(MemoryItemSchema),
  relations: z.array(z.unknown()).optional(),
});
export type SearchMemoryResponse = z.infer<typeof SearchMemoryResponseSchema>;

export const AddMemoryRequestSchema = z.object({
  messages: z.array(MessageSchema),
  config: MemoryConfigSchema.optional(),
});
export type AddMemoryRequest = z.infer<typeof AddMemoryRequestSchema>;

export const AddMemoryResponseSchema = SearchMemoryResponseSchema;
export type AddMemoryResponse = z.infer<typeof AddMemoryResponseSchema>;

export const SearchMemoryRequestSchemaV2 = z.object({
  text: z.string(),
  config: SearchMemoryOptionsSchema,
});
export type SearchMemoryRequestV2 = z.infer<typeof SearchMemoryRequestSchemaV2>;

export const GetSitesWithMemoryResponseSchema = z.object({
  sites: z.array(z.string()),
});
export type GetSitesWithMemoryResponse = z.infer<typeof GetSitesWithMemoryResponseSchema>;
