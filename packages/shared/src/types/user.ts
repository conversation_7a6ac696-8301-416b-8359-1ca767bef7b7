import { z } from 'zod';
import { ListMiniappResponseType } from './miniapp';
import { ConversationSchema } from './chat';

// payment handlers
export const OrderStatusSchema = z.enum([
  'pending',
  'completed',
  'cancelled',
  'failed',
  'finalized',
]);
export type OrderStatus = z.infer<typeof OrderStatusSchema>;

export const TransactionTypeSchema = z.enum(['credit', 'debit']);
export type TransactionType = z.infer<typeof TransactionTypeSchema>;

export const TransactionReasonSchema = z.enum([
  'new_user',
  'order_pay',
  'system_add',
  'completion',
  'coupon_code',
]);
export type TransactionReason = z.infer<typeof TransactionReasonSchema>;

export const CreditLogSchema = z.object({
  id: z.number(),
  tx_credits: z.number(),
  tx_type: z.string(),
  tx_reason: z.string().nullable().optional(),
  model: z.string().nullable().optional(),
  created_at: z.string(),
});
export type CreditLog = z.infer<typeof CreditLogSchema>;

export const GetUserResponseSchema = z.object({
  id: z.string(),
  email: z.string().nullable().optional(),
  api_key: z.string(),
  api_key_enabled: z.boolean(),
  balance: z.number(),
  permission: z.object({
    can_save_site_message: z.boolean(),
  }),
});
export type GetUserResponse = z.infer<typeof GetUserResponseSchema>;

// user handlers
export const GetUserBalanceResponseSchema = z.object({
  amount: z.number(),
});
export type GetUserBalanceDResponse = z.infer<typeof GetUserBalanceResponseSchema>;

export const RotateApiKeyResponseSchema = z.object({
  newApiKey: z.string(),
});
export type RotateApiKeyResponse = z.infer<typeof RotateApiKeyResponseSchema>;

export const GetCreditDailyRequestSchema = z.object({
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
});
export type GetCreditDailyRequest = z.infer<typeof GetCreditDailyRequestSchema>;

export const CreditDailyItemSchema = z.object({
  date: z.string(),
  credits: z.number(),
});
export type CreditDailyItem = z.infer<typeof CreditDailyItemSchema>;

export const GetCreditDailyResponseSchema = z.object({
  data: z.array(CreditDailyItemSchema),
});
export type GetCreditDailyResponse = z.infer<typeof GetCreditDailyResponseSchema>;

export const RedeemCouponRequestSchema = z.object({
  code: z.string(),
});
export type RedeemCouponRequest = z.infer<typeof RedeemCouponRequestSchema>;

export const RedeemCouponResponseSchema = z.object({
  added_credits: z.number(),
  total_credits: z.number(),
});
export type RedeemCouponResponse = z.infer<typeof RedeemCouponResponseSchema>;

// stripe handlers

export const StripeCheckoutRequestSchema = z.object({
  amount: z.number(),
});
export type StripeCheckoutRequest = z.infer<typeof StripeCheckoutRequestSchema>;

export const StripeCheckoutResponseSchema = z.object({
  order_id: z.number(),
  session_id: z.string(),
  public_key: z.string(),
});
export type StripeCheckoutResponse = z.infer<typeof StripeCheckoutResponseSchema>;

export const GenerateCouponCodeRequestSchema = z.object({
  campaign: z.string().optional(),
  credits: z.number().optional(),
  max_uses: z.number().optional(),
  expired_at: z.string().optional(),
});
export type GenerateCouponCodeRequest = z.infer<typeof GenerateCouponCodeRequestSchema>;

export const GenerateCouponCodeResponseSchema = z.object({
  coupon_code: z.string(),
});
export type GenerateCouponCodeResponse = z.infer<typeof GenerateCouponCodeResponseSchema>;

// sync user data handlers
export const SyncUserDataRequestSchema = z.object({
  startFrom: z.number().default(0),
});
export type SyncUserDataRequest = z.infer<typeof SyncUserDataRequestSchema>;

export const SyncUserDataResponseSchema = z.object({
  conversations: z.array(ConversationSchema),
  miniapps: z.array(ListMiniappResponseType),
});
export type SyncUserDataResponse = z.infer<typeof SyncUserDataResponseSchema>;
