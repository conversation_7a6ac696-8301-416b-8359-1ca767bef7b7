import React, { useState } from 'react';
import { WorkflowNodeType } from '~/background/workflow/types';
import { WebContext, WebInteractionResult } from '~/types';

type Props = { onBack: () => void };

function isWorkflowNodeType(node: any): node is WorkflowNodeType {
  return node && typeof node === 'object' && 'type' in node;
}

function sendExecuteTool(name: string, args: Record<string, unknown> = {}) {
  return new Promise<WebInteractionResult<unknown>>((resolve, reject) => {
    try {
      chrome.runtime.sendMessage(
        {
          name: 'execute-tool',
          body: { name, arguments: args },
        },
        (res: WebInteractionResult<any>) => {
          // Check for Chrome runtime errors
          if (chrome.runtime.lastError) {
            reject(new Error(`Chrome runtime error: ${chrome.runtime.lastError.message}`));
            return;
          }

          // Check if the response indicates an error
          if (res && !res.success && res.error) {
            resolve(res); // Let the calling code handle tool-level errors
            return;
          }

          resolve(res);
        }
      );
    } catch (error) {
      reject(
        new Error(
          `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      );
    }
  });
}

function sendWorkflowNodeCommand(nodeOrCode: WorkflowNodeType | string) {
  return new Promise<WebInteractionResult<any>>(resolve => {
    let body;
    if (typeof nodeOrCode === 'string') {
      // Legacy support for string input
      body = { node: { type: 'script', code: nodeOrCode, id: 'script-node' } };
    } else if (isWorkflowNodeType(nodeOrCode)) {
      // New support for node object
      body = { node: nodeOrCode };
    } else {
      throw new Error('Invalid input type for sendWorkflowNodeCommand');
    }

    chrome.runtime.sendMessage(
      {
        name: 'workflow:executeNode',
        body,
      },
      (res: WebInteractionResult<any>) => {
        resolve(res);
      }
    );
  });
}

function sendWorkflowCommand(
  workflow: WorkflowNodeType[],
  parameters: Record<string, unknown> = {}
) {
  return new Promise<WebInteractionResult<any>>(resolve => {
    chrome.runtime.sendMessage(
      {
        name: 'workflow:execute',
        body: { workflow, parameters },
      },
      (res: WebInteractionResult<any>) => {
        resolve(res);
      }
    );
  });
}

const TestWebToolkit: React.FC<Props> = ({ onBack }) => {
  const [loading, setLoading] = useState<string | null>(null);
  const [result, setResult] = useState<WebInteractionResult<any> | null>(null);
  const [selectorOrIndex, setSelectorOrIndex] = useState<string>('0');
  const [copied, setCopied] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('hello');
  const [inputClearFirst, setInputClearFirst] = useState<boolean>(true);
  const [inputPressEnter, setInputPressEnter] = useState<boolean>(false);
  const [keys, setKeys] = useState<string>('Escape');
  const [refreshTimeout, setRefreshTimeout] = useState<number>(5000);
  const [analyzeSelector, setAnalyzeSelector] = useState<string>('');
  const [webContext, setWebContext] = useState<WebContext | null>(null);
  const [generateDiff, setGenerateDiff] = useState<boolean>(false);

  const run = async (tool: string, args: Record<string, unknown> = {}) => {
    try {
      setLoading(tool);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Tool execution timeout (30 seconds)')), 10000);
      });

      args.context = webContext;
      const res = await Promise.race([sendExecuteTool(`WebToolkit_${tool}`, args), timeoutPromise]);
      if (res?.context) {
        setWebContext(res.context);
      }
      setResult(res ?? { success: false, error: 'no response' });
    } catch (error) {
      console.error('---[error] TestWebToolkit tool execution failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setResult({
        success: false,
        error: `Tool "${tool}" failed: ${errorMessage}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runWorkflowNodeCommand = async () => {
    try {
      setLoading('workflow:executeNode');
      const res = await sendWorkflowNodeCommand(`
  async function() {
    let el = null;
    while (!el) {
      el = document.querySelector('[data-testid="tweetTextarea_0"]');
      if (!el) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    const rect = el.getBoundingClientRect();
    return {x: rect.left + rect.width / 2, y: rect.top + rect.height / 2};
  }();
`);
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit workflow command failed:', error);
      setResult({
        success: false,
        error: `Workflow command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runWorkflowCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res2 = await sendWorkflowCommand(
        [
          {
            type: 'navigation',
            action: 'open',
            target: { url: 'https://x.com/compose/post' },
            id: 'open-compose-tweet',
          },
          {
            id: 'generate-tweet',
            type: 'llm',
            prompt:
              'Generate a creative tweet about AI and automation in 200 characters or less, with not tags.',
            jsonSchema: `{
              "type": "object",
              "properties": {
                "tweet": { "type": "string", "maxLength": 200 }
              },
              "required": ["tweet"]
            }`,
          },
          {
            id: 'input-tweet-content',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: 'div[role="textbox"]',
              tabId: '{{open-compose-tweet}}',
              value: '{{generate-tweet.tweet}}',
              // value: 'Hello ' + new Date().toISOString(),
            },
          },
          {
            id: 'wait-for-tweet-post',
            type: 'wait',
            duration: 5000,
          },
          {
            id: 'click-tweet-post',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: 'button[data-testid="tweetButton"]',
              tabId: '{{open-compose-tweet}}',
            },
          },
        ],
        {}
      );
      setResult(res2);
    } catch (error) {
      console.error('---[error] TestWebToolkit workflow command failed:', error);
      setResult({
        success: false,
        error: `Workflow command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runTwitterDMReplyWorkflow = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-messages',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'click-first-conversation',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="conversation"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'generate-reply',
            type: 'llm',
            prompt:
              'Based on this conversation context, generate a friendly and appropriate reply. Keep it under 280 characters. Return ONLY a JSON object with a "reply" field containing your response.\n\nConversation context:\n{{params.conversationContext}}',
            jsonSchema: `{
              "type": "object",
              "properties": {
                "reply": { "type": "string", "maxLength": 280 }
              },
              "required": ["reply"]
            }`,
          },
          {
            id: 'type-reply-message',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{generate-reply.reply}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-message',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {
          conversationContext: `
          User: Hello! How are you doing today?
          You: Hi! I'm doing great, thanks for asking. How about you?
          User: I'm good too! Just wanted to say hello.
          `,
        }
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit Twitter DM reply workflow failed:', error);
      setResult({
        success: false,
        error: `Twitter DM reply workflow failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runTwitterDMReplyWithCustomMessage = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-load',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-compose-dm',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="NewDM_Button"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-compose',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'input-recipient',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="searchPeople"]',
              value: '{{params.recipient}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-search',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'select-first-result',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="TypeaheadUser"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'click-next',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="nextButton"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'type-message',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{params.message}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-dm',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {
          recipient: 'elonmusk',
          message: 'Hello! This is an automated message from my workflow system.',
        }
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit Twitter DM with custom message failed:', error);
      setResult({
        success: false,
        error: `Twitter DM with custom message failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runLLMNodeCommand = async () => {
    try {
      setLoading('workflow:executeNode');
      const res = await sendWorkflowNodeCommand({
        id: 'test-llm',
        type: 'llm',
        prompt: 'Generate a creative tweet about AI and automation in 280 characters or less.',
        jsonSchema: `{
          "type": "object",
          "properties": {
            "tweet": { "type": "string", "maxLength": 280 }
          },
          "required": ["tweet"]
        }`,
      });
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit LLM node command failed:', error);
      setResult({
        success: false,
        error: `LLM node command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runLLMWorkflowCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://x.com/compose/post' },
          },
          {
            id: 'get-page-content',
            type: 'script',
            code: `
              return {
                title: document.title,
                url: window.location.href,
                text: document.body.innerText.substring(0, 1000)
              };
            `,
            target: { tabId: '{{open-twitter}}' },
          },
          {
            id: 'generate-tweet',
            type: 'llm',
            prompt:
              'Based on the page content, generate an engaging tweet about web automation and AI. Keep it under 280 characters.',
            jsonSchema: `{
              "type": "object",
              "properties": {
                "tweet": { "type": "string", "maxLength": 280 }
              },
              "required": ["tweet"]
            }`,
          },
          {
            id: 'get-textarea-coords',
            type: 'script',
            code: `
              let el = null;
              while (!el) {
                el = document.querySelector('div[role="textbox"]');
                if (!el) {
                  await new Promise(resolve => setTimeout(resolve, 100));
                }
              }
              const rect = el.getBoundingClientRect();
              return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
            `,
            target: { tabId: '{{open-twitter}}' },
          },
          {
            id: 'click-textarea',
            type: 'debugger',
            action: 'command',
            command: 'Input.dispatchMouseEvent',
            params: {
              type: 'mousePressed',
              x: '{{get-textarea-coords.x}}',
              y: '{{get-textarea-coords.y}}',
              button: 'left',
              clickCount: 1,
            },
            target: { tabId: '{{open-twitter}}' },
          },
          {
            id: 'type-generated-tweet',
            type: 'debugger',
            action: 'command',
            command: 'Input.insertText',
            params: { text: '{{generate-tweet}}' },
            target: { tabId: '{{open-twitter}}' },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit LLM workflow command failed:', error);
      setResult({
        success: false,
        error: `LLM workflow command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runInputLibraryCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-test-page',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://example.com' },
          },
          {
            id: 'input-test',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: 'input[name="q"]',
              value: 'test input',
              tabId: '{{open-test-page}}',
            },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit input library command failed:', error);
      setResult({
        success: false,
        error: `Input library command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runSendKeysLibraryCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-test-page',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://example.com' },
          },
          {
            id: 'press-enter',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-test-page}}',
            },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit sendKeys library command failed:', error);
      setResult({
        success: false,
        error: `SendKeys library command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runIntelligentTwitterDMReply = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-messages',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-first-conversation',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="conversation"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'extract-conversation',
            type: 'script',
            target: { tabId: '{{open-twitter}}' },
            code: `
              const messages = Array.from(document.querySelectorAll('[data-testid="tweetText"]'));
              const lastMessage = messages[messages.length - 1];
              return {
                lastMessage: lastMessage ? lastMessage.textContent : 'No message found',
                messageCount: messages.length,
                conversation: messages.slice(-3).map(m => m.textContent).join(' | ')
              };
            `,
          },
          {
            id: 'generate-reply',
            type: 'script',
            target: { tabId: '{{open-twitter}}' },
            code: `
              // For now, return a simple reply since LLMNode is not implemented yet
              const conversation = '{{extract-conversation.conversation}}';
              const lastMessage = '{{extract-conversation.lastMessage}}';
              
              // Simple reply logic
              if (lastMessage.includes('hello') || lastMessage.includes('hi')) {
                return 'Hello! How are you doing today?';
              } else if (lastMessage.includes('thanks') || lastMessage.includes('thank you')) {
                return "You're welcome! Happy to help.";
              } else {
                return "Thanks for your message! I'll get back to you soon.";
              }
            `,
          },
          {
            id: 'click-message-input',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'type-intelligent-reply',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{generate-reply}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-intelligent-reply',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit intelligent Twitter DM reply failed:', error);
      setResult({
        success: false,
        error: `Intelligent Twitter DM reply failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runSimpleTwitterDMReply = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-load',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-new-message',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="NewDM_Button"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-compose',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'type-recipient',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="searchPeople"]',
              value: '{{params.recipient}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-search',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'select-user',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="TypeaheadUser"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'click-next',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="nextButton"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'type-message',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{params.message}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-message',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {
          recipient: 'testuser',
          message: 'Hello! This is a test message from the workflow system.',
        }
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit simple Twitter DM reply failed:', error);
      setResult({
        success: false,
        error: `Simple Twitter DM reply failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const SectionTitle: React.FC<{ title: string }> = ({ title }) => (
    <div
      style={{
        fontSize: 14,
        fontWeight: 600,
        color: '#374151',
        marginTop: 16,
        marginBottom: 8,
        paddingBottom: 4,
        borderBottom: '1px solid #e5e7eb',
      }}
    >
      {title}
    </div>
  );

  const Button: React.FC<{
    onClick: () => void;
    disabled?: boolean;
    title?: string;
    children: React.ReactNode;
    variant?: 'primary' | 'secondary';
  }> = ({ onClick, disabled, title, children, variant = 'secondary' }) => (
    <button
      onClick={onClick}
      disabled={disabled || loading !== null}
      style={{
        padding: '8px 12px',
        borderRadius: 6,
        border: variant === 'primary' ? '1px solid #3b82f6' : '1px solid #d1d5db',
        background: variant === 'primary' ? '#3b82f6' : '#ffffff',
        color: variant === 'primary' ? '#ffffff' : '#374151',
        cursor: disabled || loading !== null ? 'not-allowed' : 'pointer',
        fontSize: 12,
        fontWeight: 500,
        opacity: disabled || loading !== null ? 0.6 : 1,
        transition: 'all 0.2s',
      }}
      title={title}
    >
      {loading && children?.toString().includes(loading) ? 'Running…' : children}
    </button>
  );

  const Input: React.FC<{
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    style?: React.CSSProperties;
  }> = ({ value, onChange, placeholder, style }) => (
    <input
      value={value}
      onChange={e => onChange(e.target.value)}
      placeholder={placeholder}
      style={{
        padding: '6px 8px',
        border: '1px solid #d1d5db',
        borderRadius: 4,
        fontSize: 12,
        width: 120,
        ...style,
      }}
    />
  );

  return (
    <div style={{ height: '100vh', width: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: 12,
          borderBottom: '1px solid #e5e7eb',
          background: '#f9fafb',
        }}
      >
        <button
          onClick={onBack}
          style={{
            marginRight: 12,
            padding: '6px 10px',
            border: '1px solid #d1d5db',
            borderRadius: 4,
            background: '#ffffff',
            cursor: 'pointer',
          }}
        >
          ← Back
        </button>
        <div style={{ fontWeight: 600, color: '#111827' }}>Test WebToolkit</div>
      </div>

      {/* Content */}
      <div
        style={{
          padding: 16,
          display: 'flex',
          flexDirection: 'column',
          gap: 16,
          overflowY: 'auto',
          flex: 1,
        }}
      >
        {/* Page Analysis */}
        <SectionTitle title="📊 Page Analysis" />
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Button onClick={() => run('extractText')} title="Extract text from current page">
            Extract Text
          </Button>

          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Input
              value={analyzeSelector}
              onChange={setAnalyzeSelector}
              placeholder="selector (optional)"
              style={{ width: 140 }}
            />
            <label style={{ display: 'flex', alignItems: 'center', gap: 4, fontSize: 12 }}>
              <input
                type="checkbox"
                checked={generateDiff}
                onChange={e => setGenerateDiff(e.target.checked)}
              />
              Generate Diff
            </label>
            <Button
              onClick={() =>
                run('analyzePageDOM', {
                  selector: analyzeSelector || undefined,
                  generateDiff: generateDiff,
                })
              }
              title="Analyze page DOM structure"
            >
              Analyze DOM
            </Button>
          </div>

          <Button onClick={() => run('buildDomTemplate')} title="Build DOM template">
            Build DOM Template
          </Button>
        </div>

        {/* Basic WebToolkit Tools */}
        <SectionTitle title="🛠️ Basic Tools" />
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Input
              value={selectorOrIndex}
              onChange={setSelectorOrIndex}
              placeholder="selector or index"
            />
            <Button onClick={() => run('click', { selectorOrIndex })} title="Click element">
              Click Element
            </Button>
          </div>

          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Input
              value={selectorOrIndex}
              onChange={setSelectorOrIndex}
              placeholder="selector or index"
            />
            <Button onClick={() => run('scroll', { selectorOrIndex })} title="Scroll to element">
              Scroll To Element
            </Button>
          </div>

          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Input
              value={selectorOrIndex}
              onChange={setSelectorOrIndex}
              placeholder="selector or index"
            />
            <Input value={inputValue} onChange={setInputValue} placeholder="value" />
            <Button
              onClick={() =>
                run('input', {
                  selectorOrIndex,
                  value: inputValue,
                  clearFirst: inputClearFirst,
                  pressEnterAfterInput: inputPressEnter,
                })
              }
              title="Input value into element"
            >
              Input Value
            </Button>
          </div>

          <div
            style={{ display: 'flex', gap: 16, alignItems: 'center', fontSize: 12, marginLeft: 8 }}
          >
            <label style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <input
                type="checkbox"
                checked={inputClearFirst}
                onChange={e => setInputClearFirst(e.target.checked)}
              />
              Clear First
            </label>
            <label style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <input
                type="checkbox"
                checked={inputPressEnter}
                onChange={e => setInputPressEnter(e.target.checked)}
              />
              Press Enter
            </label>
          </div>

          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Input
              value={keys}
              onChange={setKeys}
              placeholder="keys (e.g. Escape, Enter)"
              style={{ width: 160 }}
            />
            <Button onClick={() => run('sendKeys', { keys })} title="Send keyboard keys">
              Send Keys
            </Button>
          </div>

          <Button onClick={() => run('screenshot')} title="Take screenshot">
            Screenshot
          </Button>

          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <Input
              value={refreshTimeout.toString()}
              onChange={value => setRefreshTimeout(Number(value))}
              placeholder="timeout (ms)"
              style={{ width: 100 }}
            />
            <Button
              onClick={() => run('refreshPage', { timeout: refreshTimeout })}
              title="Refresh page"
            >
              Refresh Page
            </Button>
          </div>
        </div>

        {/* Workflow System */}
        <SectionTitle title="⚙️ Workflow System" />
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Button onClick={runWorkflowNodeCommand} title="Execute single workflow node">
            Execute Single Node
          </Button>
          <Button onClick={runWorkflowCommand} title="Execute complete workflow">
            Execute Workflow
          </Button>
        </div>

        {/* LLM Integration */}
        <SectionTitle title="🤖 LLM Integration" />
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Button onClick={runLLMNodeCommand} title="Test LLM node">
            Test LLM Node
          </Button>
          <Button onClick={runLLMWorkflowCommand} title="LLM workflow with page content">
            LLM Workflow
          </Button>
        </div>

        {/* Library Nodes */}
        <SectionTitle title="📚 Library Nodes" />
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Button onClick={runInputLibraryCommand} title="Test input library node">
            Test Input Library
          </Button>
          <Button onClick={runSendKeysLibraryCommand} title="Test sendKeys library node">
            Test SendKeys Library
          </Button>

          {/* Individual Library Node Tests */}
          <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap', marginTop: 8 }}>
            <Button
              onClick={() => {
                setLoading('workflow:executeNode');
                sendWorkflowNodeCommand({
                  id: 'test-click',
                  type: 'library',
                  libraryId: 'lib:click',
                  params: {
                    selector: 'button[data-testid="tweetButton"]',
                    tabId: 793206153,
                  },
                })
                  .then(setResult)
                  .catch(error => {
                    setResult({
                      success: false,
                      error: `Click node test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    });
                  })
                  .finally(() => setLoading(null));
              }}
              title="Test click library node"
            >
              Test Click Node
            </Button>

            <Button
              onClick={() => {
                setLoading('workflow:executeNode');
                sendWorkflowNodeCommand({
                  id: 'test-input',
                  type: 'library',
                  libraryId: 'lib:input',
                  params: {
                    selector: 'div[role="textbox"]',
                    value: 'test input',
                    tabId: 793206161,
                  },
                })
                  .then(setResult)
                  .catch(error => {
                    setResult({
                      success: false,
                      error: `Input node test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    });
                  })
                  .finally(() => setLoading(null));
              }}
              title="Test input library node"
            >
              Test Input Node
            </Button>

            <Button
              onClick={() => {
                setLoading('workflow:executeNode');
                sendWorkflowNodeCommand({
                  id: 'test-sendkeys',
                  type: 'library',
                  libraryId: 'lib:sendKeys',
                  params: {
                    keys: 'Enter',
                    tabId: 1,
                  },
                })
                  .then(setResult)
                  .catch(error => {
                    setResult({
                      success: false,
                      error: `SendKeys node test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    });
                  })
                  .finally(() => setLoading(null));
              }}
              title="Test sendKeys library node"
            >
              Test SendKeys Node
            </Button>
          </div>
        </div>

        {/* Twitter Automation */}
        <SectionTitle title="🐦 Twitter Automation" />
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <Button
            onClick={runTwitterDMReplyWorkflow}
            title="AI-powered DM reply workflow"
            variant="primary"
          >
            AI DM Reply Workflow
          </Button>
          <Button onClick={runTwitterDMReplyWithCustomMessage} title="Send custom DM message">
            Send Custom DM
          </Button>
          <Button
            onClick={runIntelligentTwitterDMReply}
            title="Intelligent DM reply with conversation analysis"
          >
            Intelligent DM Reply
          </Button>
          <Button onClick={runSimpleTwitterDMReply} title="Simple DM reply workflow">
            Simple DM Reply
          </Button>
        </div>

        {/* Tips */}
        <div
          style={{
            fontSize: 12,
            color: '#6b7280',
            padding: 12,
            background: '#f3f4f6',
            borderRadius: 6,
            border: '1px solid #e5e7eb',
          }}
        >
          <strong>💡 Tips:</strong> For index-based operations, try 0, 1, 2...; for selector-based
          operations, use CSS selectors like #id or .class
        </div>

        {/* Results */}
        <div
          style={{
            marginTop: 8,
            padding: 16,
            border: '1px solid #e5e7eb',
            borderRadius: 8,
            background: '#ffffff',
            flex: 1,
            minHeight: 200,
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 12,
              paddingBottom: 8,
              borderBottom: '1px solid #f3f4f6',
            }}
          >
            <div style={{ fontWeight: 600, color: '#374151' }}>📋 Results</div>
            <button
              onClick={async () => {
                try {
                  const text = JSON.stringify(result?.data ?? result, null, 2);
                  await navigator.clipboard.writeText(text ?? '');
                  setCopied(true);
                  setTimeout(() => setCopied(false), 1200);
                } catch {}
              }}
              title="Copy result"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 6,
                padding: '6px 12px',
                borderRadius: 4,
                border: '1px solid #d1d5db',
                background: '#ffffff',
                cursor: 'pointer',
                fontSize: 12,
                color: '#374151',
                transition: 'all 0.2s',
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <pre
            style={{
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              fontSize: 11,
              lineHeight: 1.4,
              color: '#374151',
              background: '#f9fafb',
              padding: 12,
              borderRadius: 4,
              border: '1px solid #f3f4f6',
              maxHeight: 400,
              overflowY: 'auto',
            }}
          >
            {result
              ? JSON.stringify(result, null, 2)
              : 'No results yet. Run a test to see output here.'}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TestWebToolkit;
