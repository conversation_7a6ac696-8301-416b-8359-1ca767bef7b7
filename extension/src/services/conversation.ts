/**
 * conversation service - manage conversation related functions
 */

import { Conversation } from '../types/conversations';
import { db } from '../storages/indexdb';
import { createApiClient } from './api/client';
import { ConversationType } from '@the-agent/shared';

export const createNewConversation = async (userId: string): Promise<Conversation> => {
  const existingConversation = await db.getConversationWithoutMessages('default');
  if (existingConversation) {
    const last_selected_at = Date.now();
    await db.updateConversation(existingConversation.id, { last_selected_at: Date.now() });
    return {
      ...existingConversation,
      last_selected_at,
    };
  }
  console.log('createNewConversation for user ', userId);
  return await createNewConversationWithType(userId, Date.now(), 'default', 'New Chat');
};

export const createNewConversationWithType = async (
  userId: string,
  convId: number,
  type: ConversationType,
  title?: string
): Promise<Conversation> => {
  const conversation: Conversation = {
    id: convId,
    title: title || '',
    user_id: userId,
    messages: [],
    last_selected_at: Date.now(),
    type,
    status: 'local',
  };
  await db.saveConversation(conversation);
  const client = await createApiClient();
  backupConversation(client, conversation).catch(error => {
    console.error('Error creating remote conversation:', error);
  });
  return conversation;
};

export async function backupConversation(
  apiClient: {
    createConversation: (p: { id: number; type?: ConversationType }) => Promise<unknown>;
  },
  conv: Conversation
) {
  // local: the conversation is not in remote and not being created by anyone else, we create it in remote
  // syncing: the conversation is being created by another caller, skip
  // remote: the conversation is already created in remote, skip
  if (conv?.status !== 'local') {
    return;
  }
  try {
    await db.updateConversation(conv.id, { status: 'syncing' });
    const convType = (conv.type ?? undefined) as ConversationType | undefined;
    await apiClient.createConversation({ id: conv.id, type: convType });
    await db.updateConversation(conv.id, { status: 'remote' });
  } catch (error) {
    await db.updateConversation(conv.id, { status: 'local' });
    throw error;
  }
}

/**
 * select conversation
 */
export const selectConversation = async (id: number) => {
  try {
    const now = Date.now();
    await db.conversations.update(id, { last_selected_at: now });
    const client = await createApiClient();
    client.updateConversation({ id, last_selected_at: now }).catch(error => {
      console.error('Error updating conversation:', error);
    });
  } catch (error) {
    console.error('Error selecting conversation:', error);
  }
};

/**
 * delete conversation
 */
export const deleteConversation = async (id: number): Promise<void> => {
  await db.deleteConversation(id);
  await db.deleteMessagesByConversation(id);
  // do not wait for it to finish for performance
  createApiClient().then(client => {
    client.deleteConversation({ id });
  });
};

/**
 * rename conversation title
 */
export const renameConversation = async (id: number, newName: string): Promise<void> => {
  await db.updateConversation(id, { title: newName });
  // do not wait for it to finish for performance
  createApiClient().then(client => {
    client.updateConversation({ id, title: newName });
  });
};
