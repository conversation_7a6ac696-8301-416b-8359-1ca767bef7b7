import { db } from '~/storages/indexdb';
import { MiniApp, Developing, Installation, MiniAppType } from '@the-agent/shared';
import { createNewConversationWithType, deleteConversation } from './conversation';
import { createApiClient } from './api/client';
import { MiniAppLocal } from '~/types/miniapp';

export const createNewMiniApp = async (
  userId: string,
  projectName: string,
  pluginType: MiniAppType
): Promise<MiniAppLocal> => {
  const now = Date.now();
  const conv = await createNewConversationWithType(userId, now, 'miniapp', `miniapp ${now}`);
  const miniapp: MiniAppLocal = {
    id: Date.now(),
    name: projectName.trim(),
    conversation_id: conv.id,
    developing: null,
    installation: null,
    history: [],
    sync_status: 'local',
    type: pluginType,
  };
  await db.saveApplication(miniapp);
  // TODO: @kevin: sync to backend
  return miniapp;
};

export const deleteMiniapp = async (id: number): Promise<void> => {
  // Find miniapp first to locate its conversation
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) return;

  // Delete associated conversation and messages using the conversationId
  if (miniapp.conversation_id != null) {
    await deleteConversation(miniapp.conversation_id);
  }

  // Remove the miniapp record itself
  await db.apps.delete(id);

  // TODO: @kevin: sync to backend
};

export const updateMiniapp = async (
  id: number,
  update: Partial<Pick<MiniApp, 'name' | 'installation' | 'status'>>
): Promise<void> => {
  const client = await createApiClient();
  await client.updateMiniApp({ id, ...update });
  await db.updateMiniapp(id, update);

  // TODO: @kevin: sync to backend
};

// Update miniapp developing field and trigger sync
export const updateMiniappDeveloping = async (
  id: number,
  developing: Developing
): Promise<void> => {
  await db.updateMiniapp(id, { developing });

  // Send sync message to web via background script
  await chrome.runtime.sendMessage({
    name: 'trigger-script-sync',
    body: {
      type: 'script-updated',
      miniappId: id,
      data: {
        code: developing.code,
        version: developing.version,
        updated_at: developing.updated_at,
      },
    },
  });

  // TODO: @kevin: sync to backend
};

export const updateMiniappInstallation = async (id: number, installation: Installation) => {
  const miniapp = await db.getMiniapp(id);
  if (!miniapp) {
    throw new Error('MiniApp not found');
  }

  // Add installation to history if code doesn't already exist in history
  const codeExistsInHistory = miniapp.history.some(
    historyInstallation => historyInstallation.code === installation.code
  );

  if (!codeExistsInHistory) {
    miniapp.history.push(installation);
  }

  miniapp.installation = installation;
  await db.updateMiniapp(id, miniapp);

  // TODO: @kevin: sync to backend
};
