import { RuntimeInput, RuntimeResolver } from './types';

/**
 * Runtime input resolver - handles dynamic variable substitution
 */
export class WorkflowRuntimeResolver implements RuntimeResolver {
  /**
   * Resolve a runtime input value using current workflow variables
   */
  resolve<T>(input: RuntimeInput<T>, variables: Record<string, string>): T {
    // If it's not a string, return as-is (static value)
    if (typeof input !== 'string') {
      return input as T;
    }

    const inputStr = input as string;

    // Check if it's a variable reference pattern {{...}}
    const variableMatch = inputStr.match(/^\{\{(.+)\}\}$/);
    if (!variableMatch) {
      return inputStr as T; // Return as string if no variable pattern
    }

    const variablePath = variableMatch[1].trim();

    // Special foreach variables
    if (variablePath === 'item') {
      const itemJson = variables['__item__'];
      if (!itemJson) {
        throw new Error('No current item in foreach context');
      }
      return JSON.parse(itemJson) as T;
    }
    if (variablePath === 'index') {
      const indexJson = variables['__index__'];
      if (!indexJson) {
        throw new Error('No current index in foreach context');
      }
      return JSON.parse(indexJson) as T;
    }

    // Handle parameter references (e.g., "params.tweetContent")
    if (variablePath.startsWith('params.')) {
      const paramName = variablePath.substring(7); // Remove "params." prefix
      const paramJson = variables[`__param__${paramName}`];
      if (paramJson === undefined) {
        throw new Error(`Parameter '${paramName}' not found in workflow parameters`);
      }
      let parsedValue = '';
      try {
        parsedValue = JSON.parse(paramJson);
      } catch (error) {
        parsedValue = paramJson;
      }
      return parsedValue as T;
    }

    // If the variable path uses dot notation, resolve the base first, then drill down
    if (variablePath.includes('.')) {
      const parts = variablePath.split('.');
      const baseName = parts[0];
      const propertyPath = parts.slice(1).join('.');

      const baseJson = variables[baseName];
      if (baseJson === undefined) {
        throw new Error(`Variable '${baseName}' not found in workflow variables`);
      }

      try {
        const baseValue = JSON.parse(baseJson);
        const nestedValue = this.getNestedProperty(baseValue, propertyPath);
        if (nestedValue === undefined) {
          throw new Error(`Property '${propertyPath}' not found in variable '${baseName}'`);
        }
        return nestedValue as T;
      } catch (error) {
        if (error instanceof SyntaxError) {
          throw new Error(`Invalid JSON in variable '${baseName}': ${error.message}`);
        }
        throw error;
      }
    }

    // Get the JSON string for the variable (no dot notation)
    const jsonString = variables[variablePath];
    if (jsonString === undefined) {
      return '' as T;
    }

    try {
      const parsedValue = JSON.parse(jsonString || '{}');
      return parsedValue as T;
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error(`Invalid JSON in variable '${variablePath}': ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Resolve all runtime inputs in an object recursively
   */
  resolveObject<T>(obj: T, variables: Record<string, string>): T {
    if (obj === null || obj === undefined) {
      return obj;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(item => this.resolveObject(item, variables)) as T;
    }

    // Handle objects
    if (typeof obj === 'object') {
      const resolved: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        resolved[key] = this.resolveObject(value, variables);
      }
      return resolved as T;
    }

    // Handle primitive values (including strings that might be variables)
    return this.resolve(obj as RuntimeInput<T>, variables);
  }

  /**
   * Get nested property from object using dot notation
   * e.g., "coords.x" from { coords: { x: 100, y: 200 } }
   */
  private getNestedProperty(obj: unknown, path: string): unknown {
    const parts = path.split('.');
    let current = obj;

    for (const part of parts) {
      if (current === null || current === undefined) {
        return undefined;
      }

      if (typeof current !== 'object') {
        return undefined;
      }

      current = (current as Record<string, unknown>)[part];
      if (current) {
        return current;
      }
    }

    return current;
  }

  /**
   * Resolve template strings with multiple variables
   * e.g., "Hello {{name}}, you have {{count}} items"
   */
  resolveTemplate(template: string, variables: Record<string, string>): string {
    // Support escaping with a leading backslash: \{{var}} -> preserved as {{var}}
    // 1) Interpolate only unescaped placeholders using negative lookbehind
    const interpolated = template.replace(/(?<!\\)\{\{([^}]+)\}\}/g, match => {
      try {
        const value = this.resolve(match, variables);
        return value !== undefined ? String(value) : match;
      } catch {
        return match; // Keep original if resolution fails
      }
    });

    // 2) Unescape any escaped placeholders: \{{...}} -> {{...}}
    return interpolated.replace(/\\\{\{/g, '{{');
  }

  /**
   * Check if a value contains variable references
   */
  hasVariables(input: unknown): boolean {
    if (typeof input === 'string') {
      return /\{\{.+?\}\}/.test(input);
    }

    if (Array.isArray(input)) {
      return input.some(item => this.hasVariables(item));
    }

    if (typeof input === 'object' && input !== null) {
      return Object.values(input).some(value => this.hasVariables(value));
    }

    return false;
  }
}

// Singleton instance
export const runtimeResolver = new WorkflowRuntimeResolver();
