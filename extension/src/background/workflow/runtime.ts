import {
  WorkflowNodeType,
  WorkflowExecutionContext,
  ExecutionResult,
  ResolvedScriptNode,
  ResolvedNavigationNode,
  ResolvedWaitNode,
  ResolvedDebuggerNode,
  ResolvedLLMNode,
  ResolvedLibraryNode,
} from './types';
import { runtimeResolver } from './runtime-resolver';
import { cdpExecutor } from './cdp-executor';
import { llmExecutor } from './llm-executor';
import { attachDebuggerIfNeeded, detachDebuggerIfAttached } from './debugger-utils';
import { userScriptManager } from './user-scripts';
import { db } from '~/storages/indexdb';
import { INPUT_NODES } from './library/input';
import { SENDKEYS_NODES } from './library/sendKeys';
import { CLICK_NODES } from './library/click';

/**
 * Main workflow runtime engine
 */
export class WorkflowRuntime {
  /**
   * Execute a single node with parameters
   */
  async executeNode(
    node: WorkflowNodeType,
    parameters: Record<string, unknown> = {}
  ): Promise<ExecutionResult> {
    const context: WorkflowExecutionContext = {
      workflowId: 'single-node-execution',
      variables: {},
      startTime: Date.now(),
      tabContexts: new Map(),
      settings: {
        defaultTimeout: 30000,
        maxRetries: 0,
        debugMode: false,
      },
    };

    // Add parameters to variables with __param__ prefix
    for (const [name, value] of Object.entries(parameters)) {
      context.variables[`__param__${name}`] = JSON.stringify(value);
    }

    return await this.executeNodeInternal(node, context);
  }

  /**
   * Execute a complete workflow
   */
  async executeWorkflow(
    nodes: WorkflowNodeType[],
    parameters: Record<string, unknown> = {}
  ): Promise<ExecutionResult> {
    const context: WorkflowExecutionContext = {
      workflowId: 'workflow-execution',
      variables: {},
      startTime: Date.now(),
      tabContexts: new Map(),
      settings: {
        defaultTimeout: 30000,
        maxRetries: 0,
        debugMode: false,
      },
    };

    // Add parameters to variables with __param__ prefix
    for (const [name, value] of Object.entries(parameters)) {
      context.variables[`__param__${name}`] =
        typeof value === 'string' ? value : JSON.stringify(value);
    }

    try {
      // Execute all nodes sequentially
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        context.currentNodeId = node.id;

        const result = await this.executeNodeInternal(node, context);

        if (!result.success) {
          return {
            success: false,
            error: `Node '${node.id}' failed: ${result.error}`,
            executionTime: Date.now() - context.startTime,
          };
        }

        // Always store result in variables using node ID
        if (result.result) {
          context.variables[node.id] =
            typeof result.result === 'string' ? result.result : JSON.stringify(result.result);
        }
      }

      // Return the result of the last node
      const lastNode = nodes[nodes.length - 1];
      const finalResult = lastNode
        ? context.variables[lastNode.id]
        : JSON.stringify({ success: true });

      return {
        success: true,
        result: finalResult ? JSON.parse(finalResult) : undefined,
        executionTime: Date.now() - context.startTime,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
        executionTime: Date.now() - context.startTime,
      };
    } finally {
      // Cleanup: detach any attached debuggers and user scripts
      await this.cleanupDebuggers();
      await userScriptManager.cleanupAll();
    }
  }

  /**
   * Execute a single node
   */
  private async executeNodeInternal(
    node: WorkflowNodeType,
    context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      // Resolve all runtime inputs in the node
      const resolvedNode = runtimeResolver.resolveObject(
        node,
        context.variables
      ) as WorkflowNodeType;

      switch (resolvedNode.type) {
        case 'script':
          return await this.executeScriptNode(resolvedNode as ResolvedScriptNode, context);
        case 'navigation':
          return await this.executeNavigationNode(resolvedNode as ResolvedNavigationNode, context);
        case 'wait':
          return await this.executeWaitNode(resolvedNode as ResolvedWaitNode, context);
        case 'debugger':
          return await this.executeDebuggerNode(resolvedNode as ResolvedDebuggerNode, context);
        case 'llm':
          return await this.executeLLMNode(resolvedNode as ResolvedLLMNode, context);
        case 'library':
          return await this.executeLibraryNode(resolvedNode as ResolvedLibraryNode, context);
        default:
          return {
            success: false,
            error: `Unknown node type: ${(node as WorkflowNodeType).type}`,
          };
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: `Failed to resolve node inputs: ${message}`,
      };
    }
  }

  /**
   * Execute a script node using user script registration
   */
  private async executeScriptNode(
    node: ResolvedScriptNode,
    context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      const tabId = node.target?.tabId ? Number(node.target.tabId) : await this.getCurrentTabId();
      // Interpolate variables inside the code string (e.g., {{params.foo}} or {{previousNode.result}})
      const interpolatedCode = runtimeResolver.resolveTemplate(node.code, context.variables);

      // Build a preamble to inject parameters and iteration helpers into the evaluation scope
      const preamble = (() => {
        const params: Record<string, unknown> = {};
        for (const [key, value] of Object.entries(context.variables)) {
          if (key.startsWith('__param__')) {
            const name = key.substring('__param__'.length);
            try {
              params[name] = JSON.parse(value);
            } catch {
              params[name] = value;
            }
          }
        }

        const helpers: Record<string, unknown> = {};
        if (context.variables.__item__) {
          try {
            helpers.$item = JSON.parse(context.variables.__item__);
          } catch {
            /* ignore */
          }
        }
        if (context.variables.__index__) {
          try {
            helpers.$index = JSON.parse(context.variables.__index__);
          } catch {
            /* ignore */
          }
        }

        // Expose as const bindings; avoid JSON circulars by stringify
        const paramsJson = JSON.stringify(params);
        const helpersJson = JSON.stringify(helpers);
        return `const $params = ${paramsJson};\nconst $item = ${helpersJson}.$item;\nconst $index = ${helpersJson}.$index;`;
      })();
      // Use CDP Runtime.evaluate to avoid CSP and immediate execution on current page
      const result = await cdpExecutor.execute({
        tabId: Number(tabId),
        code: interpolatedCode,
        preamble,
        timeoutMs: node.timeout || 30000,
      });
      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a navigation node
   */
  private async executeNavigationNode(
    node: ResolvedNavigationNode,
    _context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      let result: unknown;
      const loadTimeout = node.loadTimeout || 30000;

      switch (node.action) {
        case 'open':
          if (!node.target?.url) {
            throw new Error('URL required for open action');
          }
          const tab = await chrome.tabs.create({
            url: node.target.url,
            active: true,
          });
          result = tab.id;

          // Always wait for page to load
          if (tab.id) {
            await this.waitForTabLoad(tab.id, loadTimeout);
          }
          break;

        case 'close':
          if (!node.target?.tabId) {
            throw new Error('Tab ID required for close action');
          }
          await chrome.tabs.remove(node.target.tabId);
          result = true;
          break;

        case 'switch':
          if (!node.target?.tabId) {
            throw new Error('Tab ID required for switch action');
          }
          await chrome.tabs.update(node.target.tabId, { active: true });
          result = node.target.tabId;
          break;

        case 'reload':
          const tabId = node.target?.tabId || (await this.getCurrentTabId());
          await chrome.tabs.reload(tabId);
          result = tabId;

          // Always wait for page to reload
          await this.waitForTabLoad(tabId, loadTimeout);
          break;

        case 'back':
          const backTabId = node.target?.tabId || (await this.getCurrentTabId());
          await chrome.tabs.goBack(backTabId);
          result = backTabId;

          // Always wait for navigation to complete
          await this.waitForTabLoad(backTabId, loadTimeout);
          break;

        case 'forward':
          const forwardTabId = node.target?.tabId || (await this.getCurrentTabId());
          await chrome.tabs.goForward(forwardTabId);
          result = forwardTabId;

          // Always wait for navigation to complete
          await this.waitForTabLoad(forwardTabId, loadTimeout);
          break;

        default:
          throw new Error(`Unknown navigation action: ${node.action}`);
      }

      return {
        success: true,
        result: JSON.stringify(result),
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a wait node
   */
  private async executeWaitNode(
    node: ResolvedWaitNode,
    _context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      await new Promise(resolve => setTimeout(resolve, node.duration));

      return {
        success: true,
        result: JSON.stringify(true),
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a library node (pre-compiled or user-defined high-level workflow)
   */
  private async executeLibraryNode(
    node: ResolvedLibraryNode,
    context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      // Resolve library definition (pre-compiled or user-defined)
      const definition = await this.loadLibrary(node.libraryId);
      if (!definition) {
        throw new Error(`Library workflow not found for libraryId: ${node.libraryId}`);
      }

      // Resolve parameters using current context variables
      const resolvedParams: Record<string, unknown> = {};
      if (node.params) {
        for (const [key, value] of Object.entries(node.params)) {
          resolvedParams[key] = runtimeResolver.resolveObject(value, context.variables);
        }
      }

      // Expand into concrete nodes by injecting params
      const expandedNodes =
        typeof definition === 'function' ? await definition(resolvedParams) : definition;

      // Execute expanded nodes sequentially
      return await this.executeWorkflow(expandedNodes, resolvedParams);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  private async loadLibrary(
    id: string
  ): Promise<
    WorkflowNodeType[] | ((params: Record<string, unknown>) => Promise<WorkflowNodeType[]>) | null
  > {
    if (id.startsWith('lib:')) {
      const libraryId = id.substring('lib:'.length);
      if (libraryId === 'input') {
        return INPUT_NODES;
      } else if (libraryId === 'click') {
        return CLICK_NODES;
      } else if (libraryId === 'sendKeys') {
        return SENDKEYS_NODES;
      } else {
        throw new Error(`Unknown library: ${libraryId}`);
      }
    } else {
      const miniapp = await db.getMiniapp(Number(id));
      if (!miniapp || !miniapp.installation?.code) {
        throw new Error(`Library workflow not found for id: ${id}`);
      }
      const { workflow } = JSON.parse(miniapp.installation.code);
      return workflow;
    }
    return null;
  }

  /**
   * Execute a debugger node
   */
  private async executeDebuggerNode(
    node: ResolvedDebuggerNode,
    _context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      const tabId = node.target?.tabId || (await this.getCurrentTabId());

      switch (node.action) {
        case 'attach':
          await attachDebuggerIfNeeded(tabId, '1.0');
          return { success: true, result: JSON.stringify(true) };

        case 'detach':
          await detachDebuggerIfAttached(tabId);
          return { success: true, result: JSON.stringify(true) };

        case 'command':
          if (!node.command) {
            throw new Error('Command required for debugger command action');
          }

          // Ensure debugger is attached
          await attachDebuggerIfNeeded(tabId, '1.0');

          const result = await chrome.debugger.sendCommand(
            { tabId },
            node.command,
            node.params || {}
          );
          return { success: true, result: JSON.stringify(result) };

        default:
          throw new Error(`Unknown debugger action: ${node.action}`);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  /**
   * Execute a LLM node
   */
  private async executeLLMNode(
    node: ResolvedLLMNode,
    context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      return await llmExecutor.execute(node, context);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: message,
      };
    }
  }

  // Helper methods

  private async getCurrentTabId(): Promise<number> {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tabs[0]?.id) {
      throw new Error('No active tab found');
    }
    return tabs[0].id;
  }

  private async waitForTabLoad(tabId: number, timeout: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        chrome.tabs.onUpdated.removeListener(listener);
        reject(new Error(`Tab load timeout after ${timeout}ms`));
      }, timeout);

      const listener = (updatedTabId: number, changeInfo: chrome.tabs.TabChangeInfo) => {
        if (updatedTabId === tabId && changeInfo.status === 'complete') {
          clearTimeout(timer);
          chrome.tabs.onUpdated.removeListener(listener);
          resolve();
        }
      };

      chrome.tabs.onUpdated.addListener(listener);
    });
  }

  private async cleanupDebuggers(): Promise<void> {
    // best-effort: no-op, we now use stateless utils
  }
}

// Singleton instance
export const workflowRuntime = new WorkflowRuntime();
