import { WorkflowNodeType } from '../types';

// example of click node definition
// {
//   id: 'click-tweet-post',
//   type: 'library',
//   libraryId: 'lib:click',
//   params: {
//     selector: 'button[data-testid="tweetButton"]',
//     tabId: '{{open-compose-tweet}}',
//   },
// },

export const CLICK_NODES: WorkflowNodeType[] = [
  {
    type: 'script',
    target: { tabId: '{{params.tabId}}' },
    code: `
            let el = null;
            while (!el) {
              el = document.querySelector($params.selector);
              if (!el) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
            const rect = el.getBoundingClientRect();
            return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
          `,
    id: 'get-element-coords',
  },
  {
    id: 'click-element',
    type: 'debugger',
    command: 'Input.dispatchMouseEvent',
    action: 'command',
    params: {
      y: '{{get-element-coords.y}}',
      x: '{{get-element-coords.x}}',
      type: 'mousePressed',
      button: 'left',
      clickCount: 1,
    },
    target: { tabId: '{{params.tabId}}' },
  },
  {
    id: 'release-element-click',
    type: 'debugger',
    command: 'Input.dispatchMouseEvent',
    action: 'command',
    params: {
      y: '{{get-element-coords.y}}',
      x: '{{get-element-coords.x}}',
      type: 'mouseReleased',
      button: 'left',
      clickCount: 1,
    },
    target: { tabId: '{{params.tabId}}' },
  },
];
