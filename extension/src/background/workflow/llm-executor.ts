import OpenAI from 'openai';
import { ResolvedLLMNode, WorkflowExecutionContext, ExecutionResult } from './types';
import { db } from '~/storages/indexdb';

const DEFAULT_MODEL = 'google/gemini-2.5-flash';

/**
 * LLM Node executor for AI conversation and content generation
 */
export class LLMNodeExecutor {
  private openaiClient: OpenAI | null = null;

  /**
   * Initialize OpenAI client
   */
  private async getOpenAIClient(): Promise<OpenAI> {
    if (!this.openaiClient) {
      const model = await db.getSelectModel();
      this.openaiClient = new OpenAI({
        baseURL: model.apiUrl,
        apiKey: model.apiKey,
        dangerouslyAllowBrowser: true,
      });
    }
    return this.openaiClient;
  }

  /**
   * Execute LLM node
   */
  async execute(
    node: ResolvedLLMNode,
    _context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      const openai = await this.getOpenAIClient();
      const messages = this.prepareMessages(node);

      const response = await openai.chat.completions.create({
        model: node.model || DEFAULT_MODEL,
        messages,
      });

      const result = this.extractResponse(response);

      return {
        success: true,
        result: JSON.stringify(result),
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error(`[LLMNode] Execution failed: ${message}`);
      return {
        success: false,
        error: `LLM execution failed: ${message}`,
      };
    }
  }

  /**
   * Prepare messages for LLM
   */
  private prepareMessages(
    node: ResolvedLLMNode
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];

    // Add system prompt if provided, or add JSON enforcement prompt
    if (node.systemPrompt) {
      messages.push({
        role: 'system',
        content: node.systemPrompt,
      });
    } else if (node.jsonSchema) {
      messages.push({
        role: 'system',
        content:
          'You are a JSON generator. You must respond with valid JSON that exactly matches the provided schema. Do not include markdown formatting, explanations, or any text outside the JSON object. If you cannot generate valid JSON, return an empty object {}.',
      });
    }

    // Prepare user message
    let userContent = node.prompt;

    // If jsonSchema is provided, add JSON formatting instructions
    if (node.jsonSchema) {
      userContent += `\n\nIMPORTANT: Respond with a valid JSON object that exactly matches this schema:\n${node.jsonSchema}\n\nRequirements:
- Return ONLY the JSON object, no markdown formatting
- No explanations or additional text
- If you cannot generate valid JSON, return {}`;
    }

    messages.push({
      role: 'user',
      content: userContent,
    });
    return messages;
  }

  /**
   * Extract and format response from LLM
   */
  private extractResponse(response: OpenAI.Chat.Completions.ChatCompletion): string | object {
    const content = response.choices[0]?.message?.content;
    if (!content) {
      return {};
    }

    // Clean the content - remove leading/trailing whitespace
    const cleanContent = content.trim();

    // Try to extract JSON from markdown code blocks first
    const jsonMatch = cleanContent.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1]);
      } catch (error) {
        console.error(`[LLMNode] Failed to parse JSON from code block: ${error}`);
      }
    }

    // Try to find JSON object in the content (more flexible)
    const jsonObjectMatch = cleanContent.match(/\{[\s\S]*\}/);
    if (jsonObjectMatch) {
      try {
        return JSON.parse(jsonObjectMatch[0]);
      } catch (error) {
        console.error(`[LLMNode] Failed to parse JSON object: ${error}`);
      }
    }

    // Try direct JSON parsing
    try {
      return JSON.parse(cleanContent);
    } catch (error) {
      console.error(`[LLMNode] Failed to parse response as JSON: ${error}`);
      // Return structured error response instead of raw text
      return {
        error: 'Failed to parse LLM response as JSON',
        rawContent: cleanContent.substring(0, 200), // Limit length for debugging
      };
    }
  }
}

// Singleton instance
export const llmExecutor = new LLMNodeExecutor();
